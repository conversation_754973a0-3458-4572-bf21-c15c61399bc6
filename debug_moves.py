"""
Debug Move Engine
Test basic cube moves to identify issues
"""

from cube import Cube

def test_basic_moves():
    """Test basic move functionality."""
    print("=== Testing Basic Moves ===")
    
    cube = Cube(3)
    print(f"Initial state: {cube.is_solved()}")
    
    # Test R move
    print("\n--- Testing R move ---")
    cube.apply_move('R')
    print(f"After R: {cube.is_solved()}")
    
    # Test R' (should undo R)
    cube.apply_move("R'")
    print(f"After R': {cube.is_solved()}")
    
    # Reset and test U move
    print("\n--- Testing U move ---")
    cube.reset()
    cube.apply_move('U')
    print(f"After U: {cube.is_solved()}")
    
    cube.apply_move("U'")
    print(f"After U': {cube.is_solved()}")
    
    # Test the famous sexy move
    print("\n--- Testing Sexy Move: R U R' U' ---")
    cube.reset()
    moves = ['R', 'U', "R'", "U'"]
    
    for i, move in enumerate(moves):
        cube.apply_move(move)
        print(f"After {move}: {cube.is_solved()}")
    
    print(f"Final result: {cube.is_solved()}")
    
    # Show cube state for debugging
    print("\n--- Cube State After Sexy Move ---")
    for face in ['U', 'R', 'F', 'D', 'L', 'B']:
        print(f"{face}: {cube.faces[face]}")

def test_move_parsing():
    """Test move parsing."""
    print("\n=== Testing Move Parsing ===")
    from cube_utils import parse_move_sequence
    
    test_sequences = [
        "R U R' U'",
        "R2 U2 R2",
        "Rw U Rw'",
        "M E S"
    ]
    
    for seq in test_sequences:
        moves = parse_move_sequence(seq)
        print(f"'{seq}' -> {moves}")

if __name__ == "__main__":
    test_basic_moves()
    test_move_parsing()
