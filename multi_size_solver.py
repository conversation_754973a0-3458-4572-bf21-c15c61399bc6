"""
Multi-Size Cube Solver
Supports 2x2, 3x3, and 4x4 cubes with appropriate algorithms
"""

from cube import Cube
from cube_utils import parse_move_sequence
import random


class MultiSizeSolver:
    """
    Universal solver that adapts to different cube sizes.
    """
    
    def __init__(self):
        self.size_specific_solvers = {
            2: PocketCubeSolver(),
            3: None,  # Use existing solvers
            4: RevengesCubeSolver()
        }
    
    def solve(self, cube):
        """
        Solve cube using appropriate algorithm for its size.
        """
        size = cube.size
        
        if size == 3:
            # Use existing 3x3 solvers
            return self._solve_3x3(cube)
        elif size in self.size_specific_solvers:
            solver = self.size_specific_solvers[size]
            return solver.solve(cube)
        else:
            return {
                "success": False,
                "error": f"No solver available for {size}x{size} cube"
            }
    
    def _solve_3x3(self, cube):
        """Delegate to existing 3x3 solvers."""
        # This would integrate with kociemba_solver or beginner_solver
        return {"success": False, "error": "Use dedicated 3x3 solvers"}


class PocketCubeSolver:
    """
    Solver for 2x2 Pocket Cube.
    Uses Ortega method or CLL/EG algorithms.
    """
    
    def __init__(self):
        self.name = "2x2 Pocket Cube Solver"
    
    def solve(self, cube):
        """
        Solve 2x2 cube using Ortega method.
        
        Steps:
        1. Solve one face (usually white)
        2. Orient last layer (OLL)
        3. Permute last layer (PLL)
        """
        if cube.size != 2:
            raise ValueError("This solver only works with 2x2 cubes")
        
        moves = []
        
        # Step 1: Solve bottom face
        bottom_moves = self._solve_bottom_face(cube)
        moves.extend(bottom_moves)
        
        # Step 2: Orient last layer
        oll_moves = self._orient_last_layer(cube)
        moves.extend(oll_moves)
        
        # Step 3: Permute last layer
        pll_moves = self._permute_last_layer(cube)
        moves.extend(pll_moves)
        
        return {
            "success": cube.is_solved(),
            "moves": moves,
            "total_moves": len(moves),
            "method": "Ortega"
        }
    
    def _solve_bottom_face(self, cube):
        """Solve the bottom face (first layer)."""
        moves = []
        
        # Simplified implementation
        # In practice, this would use intuitive F2L-style solving
        # for the 2x2 case (only corners, no edges)
        
        max_attempts = 50
        attempts = 0
        
        while not self._is_bottom_face_solved(cube) and attempts < max_attempts:
            # Try random moves until bottom is solved
            # This is not optimal but demonstrates the concept
            move = random.choice(['R', "R'", 'U', "U'", 'F', "F'"])
            cube.apply_move(move)
            moves.append(move)
            attempts += 1
        
        return moves
    
    def _orient_last_layer(self, cube):
        """Orient the last layer corners."""
        moves = []
        
        # Use Sune algorithm: R U R' U R U2 R'
        sune = "R U R' U R U2 R'"
        
        # Apply Sune until top face is uniform
        max_attempts = 6  # Should not need more than 6 applications
        attempts = 0
        
        while not self._is_top_face_oriented(cube) and attempts < max_attempts:
            sune_moves = parse_move_sequence(sune)
            for move in sune_moves:
                cube.apply_move(move)
                moves.append(move)
            attempts += 1
        
        return moves
    
    def _permute_last_layer(self, cube):
        """Permute the last layer corners."""
        moves = []
        
        if cube.is_solved():
            return moves
        
        # Use T-perm or other PLL algorithms
        # For 2x2, we mainly need corner permutations
        
        # Adjacent corner swap: R U R' F' R U R' U' R' F R2 U' R'
        adj_swap = "R U R' F' R U R' U' R' F R2 U' R'"
        
        # Try different orientations and apply algorithm
        for _ in range(4):  # Try all 4 orientations
            if cube.is_solved():
                break
            
            # Apply algorithm
            alg_moves = parse_move_sequence(adj_swap)
            for move in alg_moves:
                cube.apply_move(move)
                moves.append(move)
            
            # If not solved, rotate top face
            if not cube.is_solved():
                cube.apply_move('U')
                moves.append('U')
        
        return moves
    
    def _is_bottom_face_solved(self, cube):
        """Check if bottom face is uniform."""
        d_face = cube.faces['D']
        first_color = d_face[0][0]
        return all(d_face[i][j] == first_color for i in range(2) for j in range(2))
    
    def _is_top_face_oriented(self, cube):
        """Check if top face is uniform (oriented)."""
        u_face = cube.faces['U']
        first_color = u_face[0][0]
        return all(u_face[i][j] == first_color for i in range(2) for j in range(2))


class RevengesCubeSolver:
    """
    Solver for 4x4 Revenge's Cube.
    Uses reduction method to solve like a 3x3.
    """
    
    def __init__(self):
        self.name = "4x4 Revenge's Cube Solver"
    
    def solve(self, cube):
        """
        Solve 4x4 cube using reduction method.
        
        Steps:
        1. Solve centers
        2. Pair up edges
        3. Solve like 3x3
        4. Handle parity cases
        """
        if cube.size != 4:
            raise ValueError("This solver only works with 4x4 cubes")
        
        moves = []
        
        # Step 1: Solve centers
        center_moves = self._solve_centers(cube)
        moves.extend(center_moves)
        
        # Step 2: Pair edges
        edge_moves = self._pair_edges(cube)
        moves.extend(edge_moves)
        
        # Step 3: Solve like 3x3 (simplified)
        three_moves = self._solve_as_3x3(cube)
        moves.extend(three_moves)
        
        # Step 4: Handle parity
        parity_moves = self._handle_parity(cube)
        moves.extend(parity_moves)
        
        return {
            "success": cube.is_solved(),
            "moves": moves,
            "total_moves": len(moves),
            "method": "Reduction"
        }
    
    def _solve_centers(self, cube):
        """Solve the center pieces of each face."""
        moves = []
        
        # This is a complex process that involves:
        # 1. Building cross on each center
        # 2. Completing each center
        # 3. Ensuring opposite centers are correct colors
        
        # Simplified placeholder implementation
        # In practice, this requires sophisticated algorithms
        
        return moves
    
    def _pair_edges(self, cube):
        """Pair up edge pieces to form 3x3-style edges."""
        moves = []
        
        # This involves:
        # 1. Finding matching edge pieces
        # 2. Bringing them together
        # 3. Pairing them without disturbing centers
        
        # Simplified placeholder implementation
        
        return moves
    
    def _solve_as_3x3(self, cube):
        """Solve the cube as if it were a 3x3."""
        moves = []
        
        # Once centers are solved and edges are paired,
        # the cube can be solved using 3x3 methods
        # treating paired edges as single edges
        
        return moves
    
    def _handle_parity(self, cube):
        """Handle 4x4-specific parity cases."""
        moves = []
        
        # 4x4 cubes can have parity situations that don't occur on 3x3:
        # 1. OLL parity (single edge flipped)
        # 2. PLL parity (single edge pair swapped)
        
        if self._has_oll_parity(cube):
            # OLL parity algorithm
            oll_parity = "Rw U2 x Rw U2 Rw U2 Rw' U2 Lw U2 Rw' U2 Rw U2 Rw' U2 Rw'"
            parity_moves = parse_move_sequence(oll_parity)
            for move in parity_moves:
                cube.apply_move(move)
                moves.append(move)
        
        if self._has_pll_parity(cube):
            # PLL parity algorithm
            pll_parity = "Rw U2 Rw x U2 Rw U2 Rw' U2 Lw U2 Rw' U2 Rw U2 Rw' U2 Rw' U2"
            parity_moves = parse_move_sequence(pll_parity)
            for move in parity_moves:
                cube.apply_move(move)
                moves.append(move)
        
        return moves
    
    def _has_oll_parity(self, cube):
        """Check for OLL parity case."""
        # Simplified check - in practice this would be more complex
        return False
    
    def _has_pll_parity(self, cube):
        """Check for PLL parity case."""
        # Simplified check - in practice this would be more complex
        return False


def create_solver_for_size(size):
    """Factory function to create appropriate solver for cube size."""
    if size == 2:
        return PocketCubeSolver()
    elif size == 3:
        # Return None to indicate use of existing 3x3 solvers
        return None
    elif size == 4:
        return RevengesCubeSolver()
    else:
        raise ValueError(f"No solver available for {size}x{size} cube")


def demo_multi_size():
    """Demonstrate multi-size cube solving."""
    print("=== Multi-Size Cube Solver Demo ===")
    
    sizes = [2, 4]  # Skip 3x3 as it has dedicated solvers
    
    for size in sizes:
        print(f"\n--- {size}x{size} Cube ---")
        
        # Create and scramble cube
        cube = Cube(size)
        scramble_moves = cube.scramble(15)
        print(f"Scrambled with {len(scramble_moves)} moves")
        
        # Create solver
        solver = create_solver_for_size(size)
        if solver is None:
            print("Use dedicated 3x3 solvers")
            continue
        
        # Solve
        print("Solving...")
        result = solver.solve(cube)
        
        if result["success"]:
            print(f"Solved! Used {result['total_moves']} moves")
            print(f"Method: {result['method']}")
        else:
            print("Failed to solve")
            if "error" in result:
                print(f"Error: {result['error']}")


def get_size_specific_info(size):
    """Get information about solving methods for different cube sizes."""
    info = {
        2: {
            "name": "Pocket Cube",
            "method": "Ortega Method",
            "steps": ["Solve one face", "Orient last layer", "Permute last layer"],
            "avg_moves": "8-12",
            "difficulty": "Beginner"
        },
        3: {
            "name": "Rubik's Cube",
            "method": "Layer-by-Layer / CFOP / Kociemba",
            "steps": ["Cross", "F2L", "OLL", "PLL"],
            "avg_moves": "20-25 (optimal), 50-60 (beginner)",
            "difficulty": "Intermediate"
        },
        4: {
            "name": "Revenge's Cube",
            "method": "Reduction Method",
            "steps": ["Centers", "Edge pairing", "3x3 solve", "Parity"],
            "avg_moves": "60-80",
            "difficulty": "Advanced"
        }
    }
    
    return info.get(size, {"name": "Unknown", "method": "Not implemented"})


if __name__ == "__main__":
    demo_multi_size()
    
    print("\n=== Cube Size Information ===")
    for size in [2, 3, 4]:
        info = get_size_specific_info(size)
        print(f"\n{size}x{size} - {info['name']}")
        print(f"Method: {info['method']}")
        print(f"Average moves: {info.get('avg_moves', 'Unknown')}")
        print(f"Difficulty: {info.get('difficulty', 'Unknown')}")
