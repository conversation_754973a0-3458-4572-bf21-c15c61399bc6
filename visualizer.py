"""
2D Tkinter Cube Visualizer
Interactive visualization of Rubik's Cube states with net layout
"""

import tkinter as tk
from tkinter import ttk, messagebox
from cube import Cube
from cube_utils import *


class CubeVisualizer:
    """Interactive 2D cube visualizer using Tkinter."""
    
    # Color mapping for display
    DISPLAY_COLORS = {
        'W': '#FFFFFF',  # White
        'Y': '#FFFF00',  # Yellow
        'G': '#00FF00',  # Green
        'B': '#0000FF',  # Blue
        'O': '#FF8000',  # Orange
        'R': '#FF0000'   # Red
    }
    
    def __init__(self, cube=None):
        self.cube = cube or Cube()
        self.sticker_size = 30
        self.gap = 2
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("Rubik's Cube Visualizer")
        self.root.geometry("800x600")
        
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create canvas for cube display
        self.canvas = tk.Canvas(main_frame, bg='lightgray', width=600, height=400)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Create control panel
        self.create_control_panel(main_frame)
        
        # Draw initial cube
        self.draw_cube()
    
    def create_control_panel(self, parent):
        """Create the control panel with buttons and inputs."""
        control_frame = ttk.Frame(parent, width=180)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        control_frame.pack_propagate(False)
        
        # Title
        ttk.Label(control_frame, text="Cube Controls", font=('Arial', 12, 'bold')).pack(pady=(0, 10))
        
        # Reset button
        ttk.Button(control_frame, text="Reset Cube", command=self.reset_cube).pack(fill=tk.X, pady=2)
        
        # Scramble section
        ttk.Label(control_frame, text="Scramble:").pack(anchor=tk.W, pady=(10, 2))
        scramble_frame = ttk.Frame(control_frame)
        scramble_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(scramble_frame, text="Random", command=self.scramble_cube).pack(side=tk.LEFT)
        self.scramble_length = tk.StringVar(value="25")
        ttk.Entry(scramble_frame, textvariable=self.scramble_length, width=5).pack(side=tk.RIGHT)
        
        # Move input section
        ttk.Label(control_frame, text="Apply Moves:").pack(anchor=tk.W, pady=(10, 2))
        self.move_entry = tk.Text(control_frame, height=3, width=20)
        self.move_entry.pack(fill=tk.X, pady=2)
        
        move_buttons_frame = ttk.Frame(control_frame)
        move_buttons_frame.pack(fill=tk.X, pady=2)
        ttk.Button(move_buttons_frame, text="Apply", command=self.apply_moves).pack(side=tk.LEFT)
        ttk.Button(move_buttons_frame, text="Clear", command=self.clear_moves).pack(side=tk.RIGHT)
        
        # Quick moves section
        ttk.Label(control_frame, text="Quick Moves:").pack(anchor=tk.W, pady=(10, 2))
        
        # Create grid of move buttons
        moves_frame = ttk.Frame(control_frame)
        moves_frame.pack(fill=tk.X, pady=2)
        
        basic_moves = ['R', "R'", 'L', "L'", 'U', "U'", 'D', "D'", 'F', "F'", 'B', "B'"]
        for i, move in enumerate(basic_moves):
            row = i // 3
            col = i % 3
            btn = ttk.Button(moves_frame, text=move, width=4,
                           command=lambda m=move: self.apply_single_move(m))
            btn.grid(row=row, column=col, padx=1, pady=1, sticky='ew')
        
        # Configure grid weights
        for i in range(3):
            moves_frame.columnconfigure(i, weight=1)
        
        # Status section
        ttk.Label(control_frame, text="Status:").pack(anchor=tk.W, pady=(10, 2))
        self.status_label = ttk.Label(control_frame, text="Solved", foreground='green')
        self.status_label.pack(anchor=tk.W)
        
        # Info section
        ttk.Label(control_frame, text="Info:").pack(anchor=tk.W, pady=(10, 2))
        self.info_text = tk.Text(control_frame, height=6, width=20, state=tk.DISABLED)
        self.info_text.pack(fill=tk.X, pady=2)
        
        self.update_info()
    
    def draw_cube(self):
        """Draw the cube in net layout on the canvas."""
        self.canvas.delete("all")
        
        # Calculate positions for net layout
        #     U
        # L   F   R   B
        #     D
        
        size = self.cube.size
        sticker_size = self.sticker_size
        gap = self.gap
        
        face_width = size * sticker_size + (size - 1) * gap
        face_height = face_width
        
        # Starting positions for each face
        start_x = 50
        start_y = 50
        
        face_positions = {
            'U': (start_x + face_width + gap, start_y),
            'L': (start_x, start_y + face_height + gap),
            'F': (start_x + face_width + gap, start_y + face_height + gap),
            'R': (start_x + 2 * (face_width + gap), start_y + face_height + gap),
            'B': (start_x + 3 * (face_width + gap), start_y + face_height + gap),
            'D': (start_x + face_width + gap, start_y + 2 * (face_height + gap))
        }
        
        # Draw each face
        for face, (x, y) in face_positions.items():
            self.draw_face(face, x, y)
        
        # Draw face labels
        for face, (x, y) in face_positions.items():
            label_x = x + face_width // 2
            label_y = y - 15
            self.canvas.create_text(label_x, label_y, text=face, font=('Arial', 12, 'bold'))
    
    def draw_face(self, face, start_x, start_y):
        """Draw a single face of the cube."""
        size = self.cube.size
        sticker_size = self.sticker_size
        gap = self.gap
        
        for i in range(size):
            for j in range(size):
                color_code = self.cube.faces[face][i][j]
                color = self.DISPLAY_COLORS.get(color_code, '#CCCCCC')
                
                x1 = start_x + j * (sticker_size + gap)
                y1 = start_y + i * (sticker_size + gap)
                x2 = x1 + sticker_size
                y2 = y1 + sticker_size
                
                # Draw sticker
                self.canvas.create_rectangle(x1, y1, x2, y2, 
                                           fill=color, outline='black', width=2)
                
                # Add color code text for clarity
                text_x = (x1 + x2) // 2
                text_y = (y1 + y2) // 2
                text_color = 'black' if color_code in ['W', 'Y'] else 'white'
                self.canvas.create_text(text_x, text_y, text=color_code, 
                                      fill=text_color, font=('Arial', 8, 'bold'))
    
    def reset_cube(self):
        """Reset cube to solved state."""
        self.cube.reset()
        self.draw_cube()
        self.update_status()
        self.update_info()
    
    def scramble_cube(self):
        """Apply random scramble to the cube."""
        try:
            length = int(self.scramble_length.get())
            scramble = generate_scramble(length)
            for move in scramble:
                self.cube.apply_move(move)
            
            self.draw_cube()
            self.update_status()
            self.update_info()
            
            # Show scramble in info
            self.show_info(f"Scramble applied:\n{format_move_sequence(scramble, 5)}")
            
        except ValueError:
            messagebox.showerror("Error", "Invalid scramble length")
    
    def apply_moves(self):
        """Apply moves from the text entry."""
        move_text = self.move_entry.get("1.0", tk.END).strip()
        if not move_text:
            return
        
        try:
            moves = parse_move_sequence(move_text)
            for move in moves:
                self.cube.apply_move(move)
            
            self.draw_cube()
            self.update_status()
            self.update_info()
            
            self.show_info(f"Applied {len(moves)} moves:\n{' '.join(moves)}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move sequence: {str(e)}")
    
    def apply_single_move(self, move):
        """Apply a single move and update display."""
        self.cube.apply_move(move)
        self.draw_cube()
        self.update_status()
        self.update_info()
        self.show_info(f"Applied: {move}")
    
    def clear_moves(self):
        """Clear the move entry text."""
        self.move_entry.delete("1.0", tk.END)
    
    def update_status(self):
        """Update the status label."""
        if self.cube.is_solved():
            self.status_label.config(text="Solved", foreground='green')
        else:
            self.status_label.config(text="Scrambled", foreground='red')
    
    def update_info(self):
        """Update the info display."""
        solved_cube = Cube(self.cube.size)
        distance = cube_distance(self.cube, solved_cube)
        
        info = f"Size: {self.cube.size}x{self.cube.size}\n"
        info += f"Stickers different: {distance}\n"
        info += f"State: {'Solved' if self.cube.is_solved() else 'Scrambled'}\n"
        
        # Validate cube state
        valid, message = validate_cube_state(self.cube)
        info += f"Valid: {valid}\n"
        if not valid:
            info += f"Issue: {message}\n"
        
        self.show_info(info)
    
    def show_info(self, text):
        """Display text in the info area."""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete("1.0", tk.END)
        self.info_text.insert("1.0", text)
        self.info_text.config(state=tk.DISABLED)
    
    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()


def main():
    """Create and run the cube visualizer."""
    cube = Cube(3)
    visualizer = CubeVisualizer(cube)
    visualizer.run()


if __name__ == "__main__":
    main()
